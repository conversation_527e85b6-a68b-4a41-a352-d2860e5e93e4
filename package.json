{"name": "302-image-toolbox", "version": "0.1.0", "private": true, "packageManager": "npm@10.1.0", "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@tanstack/react-query": "^5.51.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "context-filter-polyfill": "^0.3.13", "konva": "^9.3.14", "lodash": "^4.17.21", "next": "14.2.5", "react": "^18", "react-compare-slider": "^3.1.0", "react-dom": "^18", "react-icons": "^5.2.1", "react-konva": "^18.2.10", "react-markdown": "^9.0.1", "react-mobile-cropper": "^0.10.0", "react-player": "^2.16.0", "react-zoom-pan-pinch": "^3.6.1", "sass": "^1.77.8", "sharp": "^0.33.5", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "webpack": "^5.93.0", "zustand": "^4.5.4"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}