import { LocaleType } from "./index";

const en: LocaleType = {
  Symbol: "en",
  Title: 'AI Photo Toolbox - 302.AI',
  Desc: 'The Photo Generation Tool Powered By 302.AI',
  System: {
    Title: 'System',
    Wait: 'Wait',
    Back: 'Back',
    Download: 'Download',
    DownloadImage: 'Download Image',
    DownloadVideo: 'Download Video',
    CopyText: 'Copy Text',
    WaitImage: 'Image is being generated, please wait patiently for 1-5 minutes~',
    WaitVideo: 'Video is being generated, please wait patiently for 3-10 minutes~',
    WaitText: 'Text is being extracted, please wait patiently for 1-5 minutes~',
    BackgroundPLaceholder: 'Please enter the background description',
    PromptPlaceholder: 'Please enter the image modification requirements',
    UploadFaceImage: 'Upload Face Image',
    UploadNewImage: 'Upload new image',
    UploadStickImage: 'Upload Sticker',
    CleanStickImage: 'Clear Sticker',
    UploadFile: 'Click or drag to upload',
    SelectFilter: 'Select Filter',
    ContinueEdit: 'Continue Editing',
    ContinueRemove: 'Continue Removing',
    ContinueExpand: 'Continue Expanding',
    ContinueModify: 'Continue Modifying',
    Start: 'Start',
    Save: 'Save',
    Redo: 'Redo',
    Retry: 'Retry',
    Substract: 'Extract',
    Confirm: 'Confirm',
    Cancel: 'Cancel',
  },
  Error: {
    Title: 'System Error',
    TokenMiss: (domain: string) => `This tool has been disabled/deleted, for more information please visit ${domain}`, // -10001
    TokenInvalid: (domain: string) => `This tool has been disabled/deleted! For more information please visit ${domain}`, // -10002
    InternalError: (domain: string) => `Internal error, for more information please visit ${domain}`, // -10003
    AccountOut: (domain: string) => `Account overdue, for more information please visit ${domain}`, // -10004
    TokenExpired: (domain: string) => `Verification code expired, for more information please visit ${domain}`, // -10005
    TotalOut: (domain: string) => `This tool's total quota has been used up, for more information please visit ${domain}`, // -10006
    TodayOut: (domain: string) => `This tool's daily quota has been used up, for more information please visit ${domain}`, // -10007
    HourOut: (domain: string) => `This free tool's hour quota reached maximum limit. Please view ${domain} to create your own tool`, // -10012
    GenerateImageError: 'Image generation error, please try switching models or modifying prompts',
  },
  Auth: {
    Title: 'Authorization',
    NeedCode: 'Missing key',
    InputCode: 'Please enter the official 302.AI API-KEY below',
    PlaceHolder: 'sk-xxxxxxxxxxxxxxxxx',
    Submit: 'Save',
  },
  Footer: {
    Title: 'Content generated by AI, for reference only',
  },
  Home: {
    Title: 'Home',
  },
  About: {
    Title: 'About',
    Desc: 'AI Photo Toolbox',
    Loading: 'Loading...',
    CreateInfo: (user: string) => `This tool was created by 302.AI user ${user}, 302.AI is a platform for AI generation and sharing, you can create your own AI tools with one click`,
    TotalInfo: (all: number, use: number) => `The total limit for this tool is <${all}PTC>, used <${use}PTC>`,
    DayInfo: (all: number, use: number) => `The daily limit for this tool is <${all}PTC>, used <${use}PTC>`,
    RecordInfo: 'All generation records of this tool are stored locally and will not be uploaded. The user who generated this tool cannot see your generation records',
    MoreInfo: (domain: string) => `For more information please visit: ${domain}`,
  },
  History: {
    Title: 'History',
    Empty: 'No data',
    Clear: 'Clear Records',
    ItemCount: (count: number) => `A total of ${count} history records`,
  },
  Photo: {
    Title: 'AI Photo Toolbox',
    Landing: {
      Or: 'or',
      NowSupport: 'Now supporting',
      InputLinkPlaceholder: 'Please enter the link address, for example: https://302.ai',
      CreateImagePlaceholder: 'Please enter the description of the image',
      CreateImageAction: 'Generate'
    },
    Edit: {
    },
    Tool: {
      title: 'Now supporting',
      action: 'Try it now',
      list: [
        {
          id: 1,
          name: 'remove-bg',
          icon: 'remove-bg',
          title: 'Remove Background',
          desc: 'Accurately extract the main subject from the image',
        },
        {
          id: 2,
          name: 'remove-obj',
          icon: 'remove-obj',
          title: 'Object Removal',
          desc: 'Wipe out the area you want to remove',
        },
        {
          id: 3,
          name: 'replace-bg',
          icon: 'replace-bg',
          title: 'Background Replacement',
          desc: 'Quickly replace the background of an image',
        },
        {
          id: 4,
          name: 'vectorize',
          icon: 'vectorize',
          title: 'Image Vectorization',
          desc: 'Convert an image to an infinitely scalable vector image',
        },
        {
          id: 5,
          name: 'upscale',
          icon: 'upscale',
          title: 'Image Upscaling',
          desc: 'Support 2x, 4x, 8x image upscaling',
        },
        {
          id: 6,
          name: 'super-upscale',
          icon: 'super-upscale',
          title: 'Super Image Upscaling',
          desc: 'AI generates details not present in the original image',
        },
        {
          id: 7,
          name: 'colorize',
          icon: 'colorize',
          title: 'Colorize Black and White',
          desc: 'Add color to black and white photos',
        },
        {
          id: 8,
          name: 'swap-face',
          icon: 'swap-face',
          title: 'AI Face Swap',
          desc: 'Swap the faces in the image',
        },
        {
          id: 9,
          name: 'uncrop',
          icon: 'uncrop',
          title: 'Image Expansion',
          desc: 'Expand the edges of the image',
        },
        {
          id: 10,
          name: 'inpaint-img',
          icon: 'inpaint-img',
          title: 'Image Modification',
          desc: 'AI modifies the content of the image',
        },
        {
          id: 11,
          name: 'recreate-img',
          icon: 'recreate-img',
          title: 'Image to Image',
          desc: 'Generate a new image based on the current image',
        },
        {
          id: 12,
          name: 'sketch-img',
          icon: 'sketch-img',
          title: 'Sketch to Image',
          desc: 'Generate a beautiful image from a sketch',
        },
        {
          id: 13,
          name: 'crop-img',
          icon: 'crop-img',
          title: 'Image Cropping',
          desc: 'Precisely crop the image',
        },
        {
          id: 14,
          name: 'filter-img',
          icon: 'filter-img',
          title: 'Image Color Adjustment',
          desc: 'Adjust the color values of the image',
        },
        {
          id: 15,
          name: 'read-text',
          icon: 'read-text',
          title: 'Text Extraction',
          desc: 'Generate textual description from images',
        },
        {
          id: 16,
          name: 'create-video',
          icon: 'create-video',
          title: 'Video Generation',
          desc: 'Generate videos based on image content',
        },
        {
          id: 17,
          name: 'character',
          icon: 'character',
          title: 'Character Filter',
          desc: 'Add style filters to images',
        },
        {
          id: 18,
          name: 'stitching',
          icon: 'stitching',
          title: 'Image Stitching',
          desc: 'Stitch multiple images into one',
        },
        {
          id: 19,
          name: 'translate-text',
          icon: 'translate-text',
          title: 'Image Text Translation',
          desc: 'Translate the text in the image',
        },
        {
          id: 20,
          name: 'erase-text',
          icon: 'erase-text',
          title: 'Image Text Erasure',
          desc: 'Erase the text in the image',
        },
      ]
    },
    Character: {
      Title: 'Select Filter:',
      Desc: 'Select any of the following style filters to generate',
      List: [
        {
          label: 'Haute Couture Illustration',
          value: 'Haute Couture Illustration',
          icon: '/images/c01.png'
        },
        {
          label: 'Surreal Sci-Fi Realism Illustration',
          value: 'Surreal Sci-Fi Realism Illustration',
          icon: '/images/c02.png'
        },
        {
          label: 'Black and White Blockprint',
          value: 'Black and White Blockprint',
          icon: '/images/c03.png'
        },
        {
          label: 'Gemini Manga',
          value: 'Gemini Manga',
          icon: '/images/c04.png'
        },
        {
          label: 'Little Tinies Blockprint',
          value: 'Little Tinies Blockprint',
          icon: '/images/c05.png'
        },
        {
          label: 'Pop Art Illustration',
          value: 'Pop Art Illustration',
          icon: '/images/c06.png'
        },
        {
          label: 'The Point Illustration',
          value: 'The Point Illustration',
          icon: '/images/c07.png'
        },
        {
          label: 'Soft Focus 3D',
          value: 'Soft Focus 3D',
          icon: '/images/c08.png'
        },
        {
          label: 'Painted Illustration',
          value: 'Painted Illustration',
          icon: '/images/c09.png'
        },
        {
          label: 'Colorful Comicbook',
          value: 'Colorful Comicbook',
          icon: '/images/c10.png'
        },
        {
          label: 'Bold Lineart',
          value: 'Bold Lineart',
          icon: '/images/c11.png'
        },
        {
          label: 'Soft Anime Illustration',
          value: 'Soft Anime Illustration',
          icon: '/images/c12.png'
        },
      ]
    },
    ImageModels: {
      Title: '',
      Desc: '',
      List: [
        {
          name: 'Flux-Dev',
          value: 'flux-dev',
        },
        {
          name: 'Flux-Pro',
          value: 'flux-pro',
        },
        {
          name: 'Flux-Schnell',
          value: 'flux-schnell',
        },
        {
          name: 'Flux-Realism',
          value: 'flux-realism',
        },
        {
          name: 'SD3',
          value: 'sd3',
        },
        {
          name: 'SDXL-Lightning',
          value: 'sdxl-lightning',
        },
        {
          name: 'Aura-Flow',
          value: 'aura-flow',
        },
        {
          name: 'Kolors',
          value: 'kolors',
        },
        {
          name: 'Artistc QR-Code',
          value: 'qr-code',
        },
      ],
    },
    VideoModels: {
      List: [
        {
          name: 'Kling',
          value: 'kling',
        },
        {
          name: 'Runway',
          value: 'runway',
        },
        {
          name: 'Luma',
          value: 'luma',
        },
        {
          name: 'Cog',
          value: 'cog',
        },
      ]
    },
    VideoRatios: {
      List: [
        {
          name: '1:1',
          value: 1 / 1,
        },
        {
          name: '16:9',
          value: 16 / 9,
        },
        {
          name: '9:16',
          value: 9 / 16,
        },
        {
          name: '1280:768',
          value: 1280 / 768,
        },
        {
          name: '3:2',
          value: 3 / 2,
        },
        {
          name: 'Custom',
          value: 0,
        },
      ],
    },
    LangSelecter: {
      Title: 'Select Language',
      List: [
        {
          name: 'Automatic',
          value: 'auto',
        },
        {
          name: 'Chinese',
          value: 'zh',
        },
        {
          name: 'English',
          value: 'en',
        },
        {
          name: 'Japanese',
          value: 'ja',
        },
        {
          name: 'Korean',
          value: 'ko',
        },
        {
          name: 'German',
          value: 'de',
        },
        {
          name: 'French',
          value: 'fr',
        },
        {
          name: 'Arabic',
          value: 'ar',
        },
        {
          name: 'Spanish',
          value: 'es',
        },
        {
          name: 'Portuguese',
          value: 'pt',
        },
        {
          name: 'Italian',
          value: 'it',
        },
        {
          name: 'Thai',
          value: 'th',
        },
        {
          name: 'Vietnamese',
          value: 'vi',
        },
        {
          name: 'Indonesian',
          value: 'id',
        },
        {
          name: 'Malay',
          value: 'ms',
        },
        {
          name: 'Filipino',
          value: 'fil',
        },
        {
          name: 'Khmer',
          value: 'km',
        },
        {
          name: 'Burmese',
          value: 'my',
        },
        {
          name: 'Lao',
          value: 'lo',
        },
        {
          name: 'Bengali',
          value: 'bn',
        },
        {
          name: 'Hindi',
          value: 'hi',
        },
        {
          name: 'Urdu',
          value: 'ur',
        },
        {
          name: 'Tamil',
          value: 'ta',
        },
        {
          name: 'Telugu',
          value: 'te',
        },
        {
          name: 'Nepali',
          value: 'ne',
        },
        {
          name: 'Sinhala',
          value: 'si',
        },
        {
          name: 'Marathi',
          value: 'mr',
        }
      ]
    },
    LangProtect: {
      Translate: "Do not translate text on products",
      Erase: "Do not erase text on products",
    },
    BackModel: {
      Title: 'Exit?',
      Desc: 'Do you want to exit to the upload interface?',
      Action: 'Exit',
      Yes: 'Yes',
      No: 'No',
    },
    DescModel: {
      Title: 'Video Generation Requirements',
      Desc: 'You can enter specific descriptions for generating the video. If not entered, it will be automatically generated based on the image.',
      Action: 'Start',
      Placeholder: 'Enter video requirements',
      Yes: 'Confirm',
      No: 'Cancel',
    },
    MdModel: {
      Title: 'Text Extraction Result',
      Desc: 'The following is the text content extracted from the image',
      Action: 'View',
      Placeholder: 'Enter video requirements',
      Yes: 'Copy',
      No: 'Close',
    },
    RatioModel: {
      Title: 'Image Ratio',
      Desc: 'Select the aspect ratio for the generated image',
      Action: 'Generate',
      Placeholder: 'Enter video requirements',
      Yes: 'Confirm',
      No: 'Cancel',
    },
    SizeModel: {
      Title: 'Custom Canvas Ratio',
      Desc: 'Please enter the width and height ratio below',
      Action: 'Custom',
      Yes: 'Confirm',
      No: 'Cancel',
    },
  }
};

export default en;
