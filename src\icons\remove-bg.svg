<svg id="组_1" data-name="组 1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 13px;
      }

      .cls-1, .cls-2 {
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: currentColor;
      }
    </style>
  </defs>
  <path id="矩形_2_拷贝_14" data-name="矩形 2 拷贝 14" class="cls-1" d="M16.741,10.416H168.26a10,10,0,0,1,10,10V164.6a10,10,0,0,1-10,10H16.741a10,10,0,0,1-10-10V20.416A10,10,0,0,1,16.741,10.416Z"/>
  <path id="矩形_3_拷贝_14" data-name="矩形 3 拷贝 14" class="cls-2" d="M4.576,70.513l60.5-60.734a7.531,7.531,0,0,1,10.681,0l0.193,0.193a7.6,7.6,0,0,1,0,10.723L15.45,81.429a7.531,7.531,0,0,1-10.681,0l-0.193-.193A7.6,7.6,0,0,1,4.576,70.513Z"/>
  <path id="矩形_3_拷贝_17" data-name="矩形 3 拷贝 17" class="cls-2" d="M110.441,166.454l60.1-60.336a7.735,7.735,0,0,1,10.969,0l0.2,0.2a7.809,7.809,0,0,1,0,11.013l-60.1,60.336a7.736,7.736,0,0,1-10.97,0l-0.2-.2A7.809,7.809,0,0,1,110.441,166.454Z"/>
  <path id="矩形_3_拷贝_16" data-name="矩形 3 拷贝 16" class="cls-2" d="M46.7,165.845L169.927,42.129a7.942,7.942,0,0,1,11.264,0l0.2,0.2a8.018,8.018,0,0,1,0,11.309L58.166,177.358a7.943,7.943,0,0,1-11.264,0l-0.2-.2A8.019,8.019,0,0,1,46.7,165.845Z"/>
</svg>
