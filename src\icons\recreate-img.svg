<svg id="组_10" data-name="组 10" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 10px;
      }

      .cls-1, .cls-2 {
        fill-rule: evenodd;
      }

      .cls-2, .cls-3 {
        fill: currentColor;
      }
    </style>
  </defs>
  <path id="矩形_2_拷贝" data-name="矩形 2 拷贝" class="cls-1" d="M152,80c0,28.234.276,60.983,0.276,60.983a10,10,0,0,1-10,10H15.016a10,10,0,0,1-10-10V43.406a10,10,0,0,1,10-10h92.659"/>
  <path id="三角形_1" data-name="三角形 1" class="cls-2" d="M39.748,107.756a5,5,0,0,1,7.918,0L76.43,145.04a5,5,0,0,1-3.959,8.054H14.943a5,5,0,0,1-3.959-8.054Z"/>
  <path id="三角形_1_拷贝" data-name="三角形 1 拷贝" class="cls-2" d="M97.3,90.044a5,5,0,0,1,7.917,0l42.414,54.977a5,5,0,0,1-3.959,8.054H58.846a5,5,0,0,1-3.959-8.054Z"/>
  <circle id="椭圆_1" data-name="椭圆 1" class="cls-3" cx="55.516" cy="73.563" r="16.797"/>
  <path id="矩形_3_拷贝_8" data-name="矩形 3 拷贝 8" class="cls-2" d="M151.5,0.047A33.333,33.333,0,1,1,118.166,33.38,33.333,33.333,0,0,1,151.5.047ZM133.637,28h35.726a4.717,4.717,0,0,1,4.717,4.718v0.242a4.718,4.718,0,0,1-4.717,4.718H133.637a4.718,4.718,0,0,1-4.718-4.718V32.722A4.718,4.718,0,0,1,133.637,28Zm22.7-13.024V50.705a4.717,4.717,0,0,1-4.717,4.718h-0.242a4.718,4.718,0,0,1-4.718-4.718V14.98a4.718,4.718,0,0,1,4.718-4.718h0.242A4.717,4.717,0,0,1,156.338,14.98Z"/>
</svg>
