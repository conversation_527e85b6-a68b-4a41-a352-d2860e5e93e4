<svg id="组_7" data-name="组 7" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: none;
      }

      .cls-1, .cls-2, .cls-4 {
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      .cls-1 {
        stroke-width: 11px;
      }

      .cls-1, .cls-2, .cls-3 {
        fill-rule: evenodd;
      }

      .cls-2, .cls-4 {
        stroke-width: 10px;
      }

      .cls-3, .cls-4 {
        fill: currentColor;
      }
    </style>
  </defs>
  <path id="矩形_2_拷贝_3" data-name="矩形 2 拷贝 3" class="cls-1" d="M15.08,23.522h154.4a10,10,0,0,1,10,10V153.479a10,10,0,0,1-10,10H15.08a10,10,0,0,1-10-10V33.522A10,10,0,0,1,15.08,23.522Z"/>
  <path id="矩形_2_拷贝_16" data-name="矩形 2 拷贝 16" class="cls-2" d="M99.978,95.479h73.128a5,5,0,0,1,5,5v56.5a5,5,0,0,1-5,5H99.978a5,5,0,0,1-5-5v-56.5A5,5,0,0,1,99.978,95.479Z"/>
  <path id="三角形_1_拷贝_5" data-name="三角形 1 拷贝 5" class="cls-3" d="M112.848,141.218a5,5,0,0,1,7.918,0L133.254,157.4a5,5,0,0,1-3.959,8.055H104.32a5,5,0,0,1-3.959-8.055Z"/>
  <path id="三角形_1_拷贝_5-2" data-name="三角形 1 拷贝 5" class="cls-3" d="M147.342,130.91a5,5,0,0,1,7.918,0l20.431,26.483a5,5,0,0,1-3.958,8.054H130.87a5,5,0,0,1-3.959-8.054Z"/>
  <circle id="椭圆_1_拷贝_6" data-name="椭圆 1 拷贝 6" class="cls-4" cx="122.984" cy="117.969" r="6.703"/>
  <path id="矩形_3_拷贝_3" data-name="矩形 3 拷贝 3" class="cls-3" d="M46.485,56.489L74.073,83.458a5.119,5.119,0,0,1,0,7.355,5.407,5.407,0,0,1-7.524,0L38.96,63.844a5.119,5.119,0,0,1,0-7.355A5.407,5.407,0,0,1,46.485,56.489Z"/>
  <path id="矩形_3_拷贝_4" data-name="矩形 3 拷贝 4" class="cls-3" d="M41.147,52.938V74.717a5.291,5.291,0,0,1-5.291,5.291H35.8a5.291,5.291,0,0,1-5.291-5.291V52.938A5.291,5.291,0,0,1,35.8,47.647h0.059A5.291,5.291,0,0,1,41.147,52.938Z"/>
  <path id="矩形_3_拷贝_5" data-name="矩形 3 拷贝 5" class="cls-3" d="M59.03,57.471H36.269A5.172,5.172,0,0,1,31.1,52.3V52.241a5.172,5.172,0,0,1,5.172-5.172H59.03A5.172,5.172,0,0,1,64.2,52.241V52.3A5.172,5.172,0,0,1,59.03,57.471Z"/>
</svg>
