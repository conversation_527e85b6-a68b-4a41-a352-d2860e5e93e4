<svg id="组_10" data-name="组 10" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 10px;
      }

      .cls-1, .cls-3 {
        fill-rule: evenodd;
      }

      .cls-3 {
        fill: currentColor;
      }
    </style>
  </defs>
  <path id="矩形_2_拷贝" data-name="矩形 2 拷贝" class="cls-1" d="M152,80c0,28.234.276,60.983,0.276,60.983a10,10,0,0,1-10,10H15.016a10,10,0,0,1-10-10V43.406a10,10,0,0,1,10-10h92.659"/>
  <path id="三角形_1_拷贝_2" data-name="三角形 1 拷贝 2" class="cls-1" d="M19.961,150.9A5,5,0,0,1,16,142.837L41.8,109.471a5,5,0,0,1,7.912,0S56.543,119.353,64,129"/>
  <path id="三角形_1_拷贝_2-2" data-name="三角形 1 拷贝 2" class="cls-1" d="M65.6,131.136l29.372-38a5,5,0,0,1,7.911,0l38.4,49.681"/>
  <circle id="椭圆_1_拷贝" data-name="椭圆 1 拷贝" class="cls-2" cx="55.734" cy="72.047" r="13.641"/>
  <path id="矩形_3_拷贝_8" data-name="矩形 3 拷贝 8" class="cls-3" d="M151.5,0.047A33.333,33.333,0,1,1,118.166,33.38,33.333,33.333,0,0,1,151.5.047ZM133.637,28h35.726a4.717,4.717,0,0,1,4.717,4.718v0.242a4.718,4.718,0,0,1-4.717,4.718H133.637a4.718,4.718,0,0,1-4.718-4.718V32.722A4.718,4.718,0,0,1,133.637,28Zm22.7-13.024V50.705a4.717,4.717,0,0,1-4.717,4.718h-0.242a4.718,4.718,0,0,1-4.718-4.718V14.98a4.718,4.718,0,0,1,4.718-4.718h0.242A4.717,4.717,0,0,1,156.338,14.98Z"/>
</svg>
