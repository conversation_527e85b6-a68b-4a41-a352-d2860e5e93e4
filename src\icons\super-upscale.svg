<svg id="组_5" data-name="组 5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke-width: 14px;
      }

      .cls-1, .cls-3 {
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      .cls-2, .cls-3 {
        fill: currentColor;
        fill-rule: evenodd;
      }

      .cls-3 {
        stroke-width: 5px;
      }
    </style>
  </defs>
  <circle id="椭圆_2_拷贝_3" data-name="椭圆 2 拷贝 3" class="cls-1" cx="75" cy="86.89" r="68.156"/>
  <path id="矩形_3_拷贝_7" data-name="矩形 3 拷贝 7" class="cls-2" d="M47.719,79.478h54.393a7.417,7.417,0,0,1,0,14.834H47.719A7.417,7.417,0,0,1,47.719,79.478Z"/>
  <path id="矩形_3_拷贝_7-2" data-name="矩形 3 拷贝 7" class="cls-2" d="M82.332,59.7v54.393a7.417,7.417,0,0,1-14.834,0V59.7A7.417,7.417,0,1,1,82.332,59.7Z"/>
  <path id="矩形_3_拷贝_11" data-name="矩形 3 拷贝 11" class="cls-2" d="M126.668,143.059l23.55,29.464a7.679,7.679,0,1,1-12,9.589l-23.55-29.464A7.679,7.679,0,0,1,126.668,143.059Z"/>
  <path id="多边形_2_拷贝_3" data-name="多边形 2 拷贝 3" class="cls-3" d="M150.286,4.1a1.732,1.732,0,0,1,3.451,0q1.725,15.865,17.253,17.628a1.778,1.778,0,0,1,0,3.526q-15.528,1.763-17.253,17.628a1.732,1.732,0,0,1-3.451,0q-1.725-15.866-17.254-17.628a1.778,1.778,0,0,1,0-3.526Q148.561,19.965,150.286,4.1Z"/>
  <path id="多边形_2_拷贝_4" data-name="多边形 2 拷贝 4" class="cls-3" d="M171.6,50.754a0.87,0.87,0,0,1,1.733,0Q174.2,58.724,182,59.61a0.893,0.893,0,0,1,0,1.771q-7.8.886-8.668,8.856a0.87,0.87,0,0,1-1.733,0q-0.867-7.97-8.668-8.856a0.893,0.893,0,0,1,0-1.771Q170.734,58.724,171.6,50.754Z"/>
</svg>
