<svg id="组_2" data-name="组 2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 14px;
      }

      .cls-1, .cls-2 {
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: currentColor;
      }
    </style>
  </defs>
  <path id="矩形_2_拷贝_11" data-name="矩形 2 拷贝 11" class="cls-1" d="M38.14,96.612L119.462,23.4a13.265,13.265,0,0,1,18.716.968l36.89,40.863a13.222,13.222,0,0,1-.97,18.691L92.777,157.133a13.265,13.265,0,0,1-18.716-.968L37.171,115.3A13.221,13.221,0,0,1,38.14,96.612Z"/>
  <path id="矩形_3_拷贝_13" data-name="矩形 3 拷贝 13" class="cls-2" d="M52.618,151.829H167.477a8.083,8.083,0,0,1,0,16.166H52.618A8.083,8.083,0,0,1,52.618,151.829Z"/>
  <path id="矩形_3_拷贝_15" data-name="矩形 3 拷贝 15" class="cls-2" d="M8.484,151.829H21.752a8.083,8.083,0,0,1,0,16.166H8.484A8.083,8.083,0,0,1,8.484,151.829Z"/>
  <path id="矩形_3_拷贝_12" data-name="矩形 3 拷贝 12" class="cls-2" d="M75.79,64.367l42.962,44.983a6.646,6.646,0,0,1-.388,9.6l-1.857,1.663a7.182,7.182,0,0,1-9.919-.376L63.626,75.259a6.646,6.646,0,0,1,.388-9.6l1.857-1.663A7.182,7.182,0,0,1,75.79,64.367Z"/>
</svg>
