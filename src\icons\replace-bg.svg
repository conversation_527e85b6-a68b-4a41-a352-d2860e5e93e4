<svg id="组_3" data-name="组 3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1, .cls-3 {
        fill: currentColor;
      }

      .cls-1, .cls-2, .cls-3 {
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: none;
        stroke-width: 12px;
      }

      .cls-2, .cls-3 {
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      .cls-3 {
        stroke-width: 10px;
      }
    </style>
  </defs>
  <path id="三角形_2" data-name="三角形 2" class="cls-1" d="M14.894,114.163a2,2,0,0,1,3.212,0l14.586,19.645A2,2,0,0,1,31.087,137H1.914a2,2,0,0,1-1.606-3.192Z"/>
  <path id="形状_3" data-name="形状 3" class="cls-2" d="M16,133s-4.951,42,42,42"/>
  <path id="三角形_2_拷贝" data-name="三角形 2 拷贝" class="cls-1" d="M170.04,72.2a2,2,0,0,1-3.219,0L152.245,52.431a2,2,0,0,1,1.61-3.187h29.151a2,2,0,0,1,1.61,3.187Z"/>
  <path id="形状_3_拷贝" data-name="形状 3 拷贝" class="cls-2" d="M168.93,53.266s4.944-42.233-41.939-42.233"/>
  <path id="矩形_2_拷贝_12" data-name="矩形 2 拷贝 12" class="cls-2" d="M108.79,100.117h55.238a10,10,0,0,1,10,10v55.238a10,10,0,0,1-10,10H108.79a10,10,0,0,1-10-10V110.117A10,10,0,0,1,108.79,100.117Z"/>
  <path id="矩形_2_拷贝_13" data-name="矩形 2 拷贝 13" class="cls-3" d="M19.759,10.086H75a10,10,0,0,1,10,10V75.324a10,10,0,0,1-10,10H19.759a10,10,0,0,1-10-10V20.086A10,10,0,0,1,19.759,10.086Z"/>
</svg>
