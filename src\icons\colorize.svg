<svg id="组_11" data-name="组 11" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 185 185">
  <defs>
    <style>
      .cls-1, .cls-3 {
        fill: currentColor;
      }

      .cls-1, .cls-2, .cls-3 {
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: none;
        stroke-width: 13px;
      }

      .cls-2, .cls-3 {
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      .cls-3 {
        stroke-width: 10px;
      }
    </style>
  </defs>
  <path id="形状_4" data-name="形状 4" class="cls-1" d="M167,125s18,30.325,18,42a17.555,17.555,0,0,1-17,18c-7.549,0-18-4.675-18-19S167,125,167,125Z"/>
  <path id="矩形_2_拷贝_6" data-name="矩形 2 拷贝 6" class="cls-2" d="M86.467,35.644l63.2,63.2a10,10,0,0,1,0,14.142l-63.2,63.2a10,10,0,0,1-14.142,0l-63.2-63.2a10,10,0,0,1,0-14.142l63.2-63.2A10,10,0,0,1,86.467,35.644Z"/>
  <path id="矩形_2_拷贝_7" data-name="矩形 2 拷贝 7" class="cls-3" d="M150.933,110.5L87.652,173.777a9.944,9.944,0,0,1-14.062,0L10.309,110.5"/>
  <path id="矩形_3_拷贝_6" data-name="矩形 3 拷贝 6" class="cls-1" d="M61.8,1.844L94.634,34.682a6.333,6.333,0,0,1-8.956,8.956L52.841,10.8A6.333,6.333,0,1,1,61.8,1.844Z"/>
</svg>
